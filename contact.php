<?php
/**
 * نقرة هوست - معالج نموذج الاتصال
 * Nakra Host - Contact Form Handler
 */

// إعدادات الأمان
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مسموحة'
    ]);
    exit;
}

// إعدادات البريد الإلكتروني
$to_email = '<EMAIL>';
$from_email = '<EMAIL>';
$company_name = 'نقرة هوست';

// دالة تنظيف البيانات
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// دالة التحقق من البريد الإلكتروني
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// دالة التحقق من رقم الهاتف
function validate_phone($phone) {
    $phone = preg_replace('/[^0-9+\-\s\(\)]/', '', $phone);
    return strlen($phone) >= 10;
}

// استلام البيانات وتنظيفها
$name = sanitize_input($_POST['name'] ?? '');
$email = sanitize_input($_POST['email'] ?? '');
$phone = sanitize_input($_POST['phone'] ?? '');
$subject = sanitize_input($_POST['subject'] ?? '');
$message = sanitize_input($_POST['message'] ?? '');

// مصفوفة الأخطاء
$errors = [];

// التحقق من صحة البيانات
if (empty($name)) {
    $errors[] = 'الاسم مطلوب';
}

if (empty($email)) {
    $errors[] = 'البريد الإلكتروني مطلوب';
} elseif (!validate_email($email)) {
    $errors[] = 'البريد الإلكتروني غير صحيح';
}

if (!empty($phone) && !validate_phone($phone)) {
    $errors[] = 'رقم الهاتف غير صحيح';
}

if (empty($subject)) {
    $errors[] = 'الموضوع مطلوب';
}

if (empty($message)) {
    $errors[] = 'الرسالة مطلوبة';
} elseif (strlen($message) < 10) {
    $errors[] = 'الرسالة قصيرة جداً (10 أحرف على الأقل)';
}

// إذا كانت هناك أخطاء
if (!empty($errors)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'يرجى تصحيح الأخطاء التالية:',
        'errors' => $errors
    ]);
    exit;
}

// إعداد رسالة البريد الإلكتروني
$email_subject = "رسالة جديدة من موقع $company_name - $subject";

$email_body = "
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
        .content { background: #f8f9fa; padding: 20px; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #333; }
        .value { margin-top: 5px; padding: 10px; background: white; border-radius: 5px; }
        .footer { background: #333; color: white; padding: 15px; text-align: center; font-size: 12px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h2>رسالة جديدة من موقع $company_name</h2>
        </div>
        <div class='content'>
            <div class='field'>
                <div class='label'>الاسم:</div>
                <div class='value'>$name</div>
            </div>
            <div class='field'>
                <div class='label'>البريد الإلكتروني:</div>
                <div class='value'>$email</div>
            </div>";

if (!empty($phone)) {
    $email_body .= "
            <div class='field'>
                <div class='label'>رقم الهاتف:</div>
                <div class='value'>$phone</div>
            </div>";
}

$email_body .= "
            <div class='field'>
                <div class='label'>الموضوع:</div>
                <div class='value'>$subject</div>
            </div>
            <div class='field'>
                <div class='label'>الرسالة:</div>
                <div class='value'>" . nl2br($message) . "</div>
            </div>
            <div class='field'>
                <div class='label'>تاريخ الإرسال:</div>
                <div class='value'>" . date('Y-m-d H:i:s') . "</div>
            </div>
            <div class='field'>
                <div class='label'>عنوان IP:</div>
                <div class='value'>" . $_SERVER['REMOTE_ADDR'] . "</div>
            </div>
        </div>
        <div class='footer'>
            <p>هذه الرسالة تم إرسالها من نموذج الاتصال في موقع $company_name</p>
            <p>للرد على هذه الرسالة، يرجى استخدام البريد الإلكتروني: $email</p>
        </div>
    </div>
</body>
</html>";

// إعداد headers البريد الإلكتروني
$headers = [
    'MIME-Version: 1.0',
    'Content-type: text/html; charset=UTF-8',
    "From: $company_name <$from_email>",
    "Reply-To: $name <$email>",
    'X-Mailer: PHP/' . phpversion(),
    'X-Priority: 3',
    'X-MSMail-Priority: Normal'
];

// محاولة إرسال البريد الإلكتروني
$mail_sent = mail($to_email, $email_subject, $email_body, implode("\r\n", $headers));

if ($mail_sent) {
    // حفظ الرسالة في ملف log (اختياري)
    $log_entry = date('Y-m-d H:i:s') . " - رسالة جديدة من: $name ($email) - الموضوع: $subject\n";
    file_put_contents('contact_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
    
    // إرسال رد تلقائي للمرسل
    $auto_reply_subject = "شكراً لتواصلك مع $company_name";
    $auto_reply_body = "
    <!DOCTYPE html>
    <html dir='rtl' lang='ar'>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #10b981; color: white; padding: 20px; text-align: center; }
            .content { background: #f8f9fa; padding: 20px; line-height: 1.6; }
            .footer { background: #333; color: white; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>شكراً لتواصلك معنا</h2>
            </div>
            <div class='content'>
                <p>عزيزي/عزيزتي <strong>$name</strong>،</p>
                <p>شكراً لك على تواصلك مع $company_name. لقد تم استلام رسالتك بنجاح وسيقوم فريقنا بالرد عليك في أقرب وقت ممكن.</p>
                <p><strong>تفاصيل رسالتك:</strong></p>
                <ul>
                    <li><strong>الموضوع:</strong> $subject</li>
                    <li><strong>تاريخ الإرسال:</strong> " . date('Y-m-d H:i:s') . "</li>
                </ul>
                <p>إذا كانت رسالتك تتطلب اهتماماً عاجلاً، يمكنك التواصل معنا مباشرة على:</p>
                <ul>
                    <li><strong>الهاتف:</strong> +201028351237</li>
                    <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
                </ul>
                <p>نتطلع لخدمتك!</p>
                <p>مع أطيب التحيات،<br>فريق $company_name</p>
            </div>
            <div class='footer'>
                <p>$company_name - حلولك الرقمية المتكاملة</p>
                <p>هذه رسالة تلقائية، يرجى عدم الرد عليها</p>
            </div>
        </div>
    </body>
    </html>";
    
    $auto_reply_headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        "From: $company_name <$from_email>",
        'X-Mailer: PHP/' . phpversion(),
        'X-Auto-Response-Suppress: All'
    ];
    
    mail($email, $auto_reply_subject, $auto_reply_body, implode("\r\n", $auto_reply_headers));
    
    // إرجاع رد نجح
    echo json_encode([
        'success' => true,
        'message' => 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.'
    ]);
} else {
    // إرجاع رد فشل
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'عذراً، حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.'
    ]);
}
?>
