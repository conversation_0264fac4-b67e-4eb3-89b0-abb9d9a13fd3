/* ===== Professional CSS Variables ===== */
:root {
    /* Primary Colors - Clean Blue Palette */
    --primary-color: #2563eb;
    --primary-light: #60a5fa;
    --primary-dark: #1d4ed8;

    /* Secondary Colors - Modern Green */
    --secondary-color: #10b981;
    --secondary-light: #34d399;
    --secondary-dark: #059669;

    /* Accent Colors - Professional Orange */
    --accent-color: #f59e0b;
    --accent-light: #fbbf24;
    --accent-dark: #d97706;
    
    /* Additional Professional Colors */
    --purple-color: #7c3aed;
    --purple-light: #8b5cf6;
    --purple-dark: #6d28d9;
    
    --pink-color: #db2777;
    --pink-light: #ec4899;
    --pink-dark: #be185d;
    
    --cyan-color: #0891b2;
    --cyan-light: #06b6d4;
    --cyan-dark: #0e7490;
    
    --yellow-color: #d97706;
    --yellow-light: #f59e0b;
    --yellow-dark: #b45309;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Clean Professional Gradients */
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    --gradient-secondary: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    --gradient-purple: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
    --gradient-pink: linear-gradient(135deg, #ec4899 0%, #f472b6 100%);
    --gradient-cyan: linear-gradient(135deg, #06b6d4 0%, #22d3ee 100%);
    --gradient-yellow: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);

    /* Clean Hero Gradients */
    --gradient-hero: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    --gradient-hero-overlay: linear-gradient(135deg, rgba(37, 99, 235, 0.95) 0%, rgba(30, 64, 175, 0.95) 100%);
    
    /* Background Gradients */
    --gradient-bg-light: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    --gradient-bg-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    
    /* Service Specific Gradients */
    --gradient-hosting: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    --gradient-marketing: linear-gradient(135deg, #db2777 0%, #ec4899 100%);
    --gradient-development: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
    --gradient-support: linear-gradient(135deg, #059669 0%, #10b981 100%);
    
    /* Fonts */
    --font-arabic: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-english: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    
    /* Font Sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Enhanced Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Colored Shadows */
    --shadow-primary: 0 10px 25px -5px rgba(30, 64, 175, 0.3);
    --shadow-secondary: 0 10px 25px -5px rgba(5, 150, 105, 0.3);
    --shadow-accent: 0 10px 25px -5px rgba(234, 88, 12, 0.3);
    --shadow-purple: 0 10px 25px -5px rgba(124, 58, 237, 0.3);
    --shadow-pink: 0 10px 25px -5px rgba(219, 39, 119, 0.3);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== Professional Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-arabic);
    line-height: 1.6;
    color: var(--gray-700);
    background: var(--white);
    overflow-x: hidden;
}

body[dir="ltr"] {
    font-family: var(--font-english);
}

/* ===== Professional Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--gray-600);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-normal);
}

a:hover {
    color: var(--primary-dark);
}

/* ===== Professional Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
    border: none;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.35);
    color: var(--white);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.25);
    border: none;
}

.btn-secondary:hover {
    background: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.35);
    color: var(--white);
}

.btn-accent {
    background: var(--accent-color);
    color: var(--white);
    box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.25);
    border: none;
}

.btn-accent:hover {
    background: var(--accent-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(245, 158, 11, 0.35);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.25);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-lg);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* ===== Professional Cards ===== */
.card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2xl);
    transition: var(--transition-normal);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-light);
}

.card-gradient {
    background: var(--gradient-bg-light);
}

.card-primary::before {
    background: var(--gradient-primary);
}

.card-secondary::before {
    background: var(--gradient-secondary);
}

.card-accent::before {
    background: var(--gradient-accent);
}

.card-purple::before {
    background: var(--gradient-purple);
}

/* ===== Professional Icons ===== */
.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-lg);
}

.icon-primary {
    background: var(--primary-color);
    color: var(--white);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.25);
}

.icon-secondary {
    background: var(--secondary-color);
    color: var(--white);
    box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.25);
}

.icon-accent {
    background: var(--accent-color);
    color: var(--white);
    box-shadow: 0 8px 25px 0 rgba(245, 158, 11, 0.25);
}

.icon-purple {
    background: var(--purple-color);
    color: var(--white);
    box-shadow: 0 8px 25px 0 rgba(124, 58, 237, 0.25);
}

.icon-pink {
    background: var(--pink-color);
    color: var(--white);
    box-shadow: 0 8px 25px 0 rgba(219, 39, 119, 0.25);
}

.icon-lg {
    width: 80px;
    height: 80px;
    font-size: var(--font-size-3xl);
}

.icon-xl {
    width: 100px;
    height: 100px;
    font-size: var(--font-size-4xl);
}

/* ===== Professional Gradients ===== */
.gradient-text {
    background: var(--gradient-hero);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 900;
}

.gradient-bg {
    background: var(--gradient-hero);
}

.gradient-bg-light {
    background: var(--gradient-bg-light);
}

.gradient-overlay {
    background: var(--gradient-hero-overlay);
}

/* ===== Professional Layout ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section {
    padding: var(--spacing-4xl) 0;
    margin: var(--spacing-2xl) 0;
}

.section-header {
    margin-bottom: var(--spacing-4xl);
    padding: 0 var(--spacing-lg);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-primary);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 900;
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.text-center {
    text-align: center;
}

/* ===== Professional Header ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    z-index: var(--z-fixed);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.navbar {
    padding: var(--spacing-lg) 0;
    position: relative;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    text-decoration: none;
}

.logo {
    height: 45px;
    width: auto;
}

.brand-name {
    font-size: var(--font-size-2xl);
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    font-family: var(--font-arabic);
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--gray-700);
    font-weight: 500;
    transition: var(--transition-normal);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* ===== Dropdown Menu ===== */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-md) 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    z-index: var(--z-dropdown);
    list-style: none;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition-normal);
    font-size: var(--font-size-sm);
}

.dropdown-menu a:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

/* ===== Mobile Menu ===== */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--gray-700);
    border-radius: 2px;
    transition: var(--transition-normal);
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== Professional Hero ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: calc(80px + var(--spacing-4xl)) 0 var(--spacing-4xl);
    margin-top: 0;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: 900;
    color: var(--white);
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 900;
    color: var(--white);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
}

.hero-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--white);
    transition: var(--transition-normal);
    animation: float 6s ease-in-out infinite;
}

.floating-card:nth-child(2) {
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    animation-delay: 4s;
}

.floating-card:nth-child(4) {
    animation-delay: 6s;
}

.floating-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.2);
}

.floating-card h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--white);
}

.floating-card p {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8);
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: var(--transition-normal);
}

.scroll-arrow:hover {
    border-color: var(--white);
    color: var(--white);
}

/* ===== Professional Services ===== */
.services {
    padding: var(--spacing-4xl) 0;
    background: var(--gray-50);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-4xl);
    padding: 0 var(--spacing-lg);
}

.service-card {
    text-align: center;
    transition: var(--transition-normal);
    padding: var(--spacing-3xl);
    margin-bottom: var(--spacing-xl);
}

.service-header {
    margin-bottom: var(--spacing-xl);
}

.service-card h3 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

.service-card p {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.service-features {
    margin-bottom: var(--spacing-xl);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
    color: var(--gray-700);
}

.feature-item i {
    color: var(--secondary-color);
    font-size: var(--font-size-lg);
}

/* ===== Professional Pricing ===== */
.pricing {
    padding: var(--spacing-4xl) 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.pricing-card {
    text-align: center;
    position: relative;
    transition: var(--transition-normal);
}

.pricing-card.featured {
    transform: scale(1.05);
    border: 2px solid var(--primary-color);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    box-shadow: var(--shadow-primary);
}

.pricing-header {
    margin-bottom: var(--spacing-xl);
}

.plan-icon {
    margin-bottom: var(--spacing-lg);
}

.plan-name {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-lg);
    color: var(--gray-900);
}

.plan-price {
    margin-bottom: var(--spacing-xl);
}

.price-monthly {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.currency {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    font-weight: 600;
}

.amount {
    font-size: var(--font-size-5xl);
    font-weight: 900;
    color: var(--primary-color);
}

.period {
    font-size: var(--font-size-base);
    color: var(--gray-600);
}

.price-yearly {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.pricing-features {
    margin-bottom: var(--spacing-xl);
    text-align: right;
}

.pricing-footer {
    margin-top: var(--spacing-xl);
}

.guarantee-text {
    margin-top: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

/* ===== Additional Services ===== */
.additional-services {
    margin-top: var(--spacing-4xl);
    padding-top: var(--spacing-3xl);
    border-top: 1px solid var(--gray-200);
}

.services-title {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--gray-900);
}

.service-price-card {
    text-align: center;
    padding: var(--spacing-2xl);
}

.service-icon {
    margin-bottom: var(--spacing-lg);
}

.service-price {
    margin: var(--spacing-lg) 0;
    font-size: var(--font-size-lg);
    color: var(--gray-600);
}

.service-price .price {
    font-size: var(--font-size-3xl);
    font-weight: 900;
    color: var(--primary-color);
    margin: 0 var(--spacing-sm);
}

/* ===== Professional Contact ===== */
.contact {
    padding: var(--spacing-4xl) 0;
    background: var(--gray-50);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-3xl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.contact-card {
    text-align: center;
    padding: var(--spacing-xl);
}

.contact-card h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

.contact-card p {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
}

.contact-form-wrapper {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
}

.contact-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
    background: var(--white);
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== Professional Footer ===== */
.footer {
    color: var(--white);
    padding: var(--spacing-4xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-3xl);
}

.footer-section h3,
.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.footer-logo .logo {
    height: 40px;
    width: auto;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-normal);
    text-decoration: none;
}

.footer-links a:hover {
    color: var(--white);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-normal);
    text-decoration: none;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.contact-info-footer {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
    color: var(--primary-light);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    font-size: var(--font-size-sm);
}

.footer-bottom a {
    color: var(--primary-light);
    text-decoration: none;
    font-weight: 600;
}

.footer-bottom a:hover {
    text-decoration: underline;
}

/* ===== Back to Top ===== */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    left: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: var(--white);
    font-size: var(--font-size-lg);
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: var(--z-fixed);
    box-shadow: var(--shadow-primary);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl), var(--shadow-primary);
}

/* ===== Animations ===== */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* ===== Language Switcher ===== */
.language-switcher {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    z-index: var(--z-tooltip);
}

.lang-btn {
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    box-shadow: var(--shadow-sm);
}

.lang-btn:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
    color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

/* ===== Responsive Design ===== */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }

    .hero-cards {
        grid-template-columns: 1fr 1fr;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .pricing-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .section {
        padding: var(--spacing-3xl) 0;
        margin: var(--spacing-lg) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-2xl);
        padding: 0 var(--spacing-md);
    }

    .section-title {
        font-size: var(--font-size-3xl);
    }

    .hero {
        padding: calc(70px + var(--spacing-2xl)) 0 var(--spacing-2xl);
        min-height: 80vh;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-stats {
        flex-direction: row;
        justify-content: space-around;
        gap: var(--spacing-md);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .hero-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .floating-card {
        padding: var(--spacing-xl);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        padding: 0 var(--spacing-md);
    }

    .service-card {
        padding: var(--spacing-2xl);
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .pricing-card.featured {
        transform: none;
    }

    .contact-form .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .language-switcher {
        top: var(--spacing-sm);
        right: var(--spacing-sm);
    }

    .navbar {
        padding: var(--spacing-md) 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .section {
        padding: var(--spacing-2xl) 0;
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
    }

    .btn-lg {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    .floating-card {
        padding: var(--spacing-md);
    }

    .service-card,
    .pricing-card,
    .contact-card {
        padding: var(--spacing-lg);
    }

    .icon-lg {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }

    .back-to-top {
        bottom: var(--spacing-md);
        left: var(--spacing-md);
        width: 45px;
        height: 45px;
    }

    .language-switcher {
        top: var(--spacing-md);
        right: var(--spacing-md);
    }
}

/* ===== Print Styles ===== */
@media print {
    .header,
    .language-switcher,
    .back-to-top,
    .scroll-indicator {
        display: none !important;
    }

    .hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }

    .section {
        padding: var(--spacing-lg) 0;
        break-inside: avoid;
    }

    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }

    * {
        color: black !important;
        background: white !important;
    }

    .gradient-text {
        -webkit-text-fill-color: black !important;
    }
}

/* ===== High Contrast Mode ===== */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #006400;
        --accent-color: #8B4513;
        --gray-600: #000000;
        --gray-700: #000000;
        --gray-900: #000000;
    }

    .card {
        border: 2px solid var(--gray-900);
    }

    .btn {
        border: 2px solid currentColor;
    }
}

/* ===== Reduced Motion ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .floating-card {
        animation: none;
    }

    .hero-particles {
        animation: none;
    }
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1a1a1a;
        --gray-50: #2a2a2a;
        --gray-100: #3a3a3a;
        --gray-200: #4a4a4a;
        --gray-300: #5a5a5a;
        --gray-400: #6a6a6a;
        --gray-500: #7a7a7a;
        --gray-600: #cccccc;
        --gray-700: #dddddd;
        --gray-800: #eeeeee;
        --gray-900: #ffffff;
    }

    .header {
        background: rgba(26, 26, 26, 0.95);
        border-bottom-color: var(--gray-200);
    }

    .card {
        background: var(--gray-50);
        border-color: var(--gray-200);
    }

    .lang-btn {
        background: rgba(26, 26, 26, 0.9);
        border-color: var(--gray-200);
        color: var(--gray-700);
    }
}
