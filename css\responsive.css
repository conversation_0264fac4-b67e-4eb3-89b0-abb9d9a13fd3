/* ===== Responsive Design ===== */

/* Extra Large Devices (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3.5rem;
    }
}

/* Large Devices (1200px and up) */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-content {
        gap: var(--spacing-2xl);
    }
}

/* Medium Devices (992px and up) */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .nav-list {
        gap: var(--spacing-lg);
    }
}

/* Small Devices (768px and up) */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 var(--spacing-md);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
    
    .tab-buttons {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .tab-btn {
        flex: 1;
        min-width: 150px;
    }
}

/* Extra Small Devices (576px and up) */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 var(--spacing-md);
    }
    
    .hero {
        min-height: 80vh;
        padding: var(--spacing-3xl) 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .btn-lg {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .nav-brand .brand-name {
        font-size: var(--font-size-xl);
    }
    
    .features {
        padding: var(--spacing-2xl) 0;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .feature-card {
        padding: var(--spacing-xl);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    .services {
        padding: var(--spacing-2xl) 0;
    }
    
    .tab-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .tab-btn {
        width: 100%;
        text-align: center;
    }
    
    .tab-content {
        padding: var(--spacing-xl);
    }
    
    .hero-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
        max-width: 100%;
    }

    .floating-card {
        padding: var(--spacing-lg);
        font-size: var(--font-size-sm);
        min-height: 120px;
    }

    .floating-card .icon {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }

    .floating-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }

    .floating-card p {
        font-size: var(--font-size-sm);
    }
}

/* Mobile Devices (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    /* Header Adjustments */
    .navbar {
        padding: var(--spacing-sm) 0;
    }
    
    .nav-brand {
        gap: var(--spacing-sm);
    }
    
    .logo {
        height: 32px;
    }
    
    .brand-name {
        font-size: var(--font-size-lg);
    }
    
    .nav-actions {
        gap: var(--spacing-sm);
    }
    
    .nav-actions .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    /* Language Switcher */
    .language-switcher {
        top: var(--spacing-sm);
        left: var(--spacing-sm);
    }
    
    .lang-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    /* Hero Section */
    .hero {
        min-height: 70vh;
        padding: var(--spacing-2xl) 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-md);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-lg);
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .btn-lg {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }
    
    .hero-image {
        order: -1;
    }
    
    .hero-graphic {
        height: 300px;
    }
    
    .hero-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        max-width: 280px;
        margin: 0 auto;
    }

    .floating-card {
        padding: var(--spacing-lg);
        font-size: var(--font-size-sm);
        min-height: 140px;
        gap: var(--spacing-sm);
    }

    .floating-card .icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-md);
    }

    .floating-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }

    .floating-card p {
        font-size: var(--font-size-sm);
    }
    
    /* Sections */
    .features,
    .services {
        padding: var(--spacing-xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    .section-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-sm);
    }
    
    .section-subtitle {
        font-size: var(--font-size-sm);
    }
    
    /* Features */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .feature-card {
        padding: var(--spacing-lg);
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .feature-card h3 {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-sm);
    }
    
    .feature-card p {
        font-size: var(--font-size-sm);
    }
    
    /* Services */
    .tab-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .tab-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .tab-content {
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
    }
    
    /* Scroll Indicator */
    .scroll-indicator {
        bottom: var(--spacing-md);
    }
    
    .scroll-arrow {
        height: 20px;
    }
}

/* Landscape Mobile Devices */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }
    
    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        text-align: left;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-buttons {
        flex-direction: row;
        gap: var(--spacing-md);
    }
    
    .btn-lg {
        width: auto;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .header,
    .language-switcher,
    .hero-buttons,
    .scroll-indicator {
        display: none !important;
    }
    
    .hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }
    
    .hero-background {
        display: none;
    }
    
    .hero-text {
        color: var(--gray-900) !important;
    }
    
    .section-title,
    .hero-title {
        color: var(--gray-900) !important;
    }
    
    .feature-card,
    .tab-content {
        box-shadow: none !important;
        border: 1px solid var(--gray-300) !important;
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .floating-card,
    .hero-particles,
    .scroll-arrow {
        animation: none !important;
    }
    
    .animate-fade-up,
    .animate-fade-left,
    .animate-fade-right,
    .animate-on-scroll {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --gray-50: #111827;
        --gray-100: #1f2937;
        --gray-200: #374151;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
    
    .header {
        background: rgba(31, 41, 55, 0.95);
        border-bottom-color: var(--gray-700);
    }
    
    .dropdown-menu {
        background: var(--gray-800);
        border: 1px solid var(--gray-700);
    }
    
    .lang-btn {
        background: var(--gray-800);
        border-color: var(--gray-700);
        color: var(--gray-200);
    }
}
