# ===== Nakra Host Website .gitignore =====

# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Editor and IDE Files
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace
*.code-workspace

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Build outputs
build/
dist/
out/

# Compressed files
*.zip
*.tar.gz
*.rar

# Image optimization cache
.image-cache/

# Backup files
*.bak
*.backup
*.old

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.local.js
config.production.js
.env.production

# SSL certificates
*.pem
*.key
*.crt

# Analytics and tracking
.analytics/
.tracking/

# Performance monitoring
.performance/
lighthouse-reports/

# Testing
coverage/
.nyc_output/
test-results/
screenshots/

# Documentation build
docs/build/
docs/.vuepress/dist/

# Deployment
.deploy/
.deployment/
deploy.log

# CDN and asset optimization
.cdn-cache/
.asset-cache/

# Service worker cache
sw-cache/

# PWA build files
workbox-*.js

# Monitoring and logging
.monitoring/
error.log
access.log

# Development tools
.dev-tools/
.debug/

# Custom project files
.project-notes/
TODO.md
NOTES.md

# Sensitive information
secrets.json
api-keys.json
credentials.json

# Local development
.local/
local-config.js

# Vendor files (if not using package manager)
vendor/
third-party/

# Generated files
generated/
auto-generated/

# Cache directories
.cache/
.tmp/

# Lock files (choose one based on your package manager)
# package-lock.json  # Uncomment if using yarn
# yarn.lock          # Uncomment if using npm

# OS generated files
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk
