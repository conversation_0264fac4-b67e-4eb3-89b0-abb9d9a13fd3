# ===== نقرة هوست - إعدادات Apache =====
# Nakra Host - Apache Configuration

# تفعيل إعادة الكتابة
RewriteEngine On

# إعادة توجيه إلى HTTPS (إذا كان متاحاً)
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www إلى non-www (أو العكس حسب التفضيل)
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# الصفحة الرئيسية
DirectoryIndex index.php index.html

# إعادة توجيه الصفحات القديمة
Redirect 301 /login.html https://hostmeed.cloud/clientarea.php
Redirect 301 /register.html https://hostmeed.cloud/register.php

# ===== أمان الموقع =====

# منع الوصول للملفات الحساسة
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.ini">
    Order allow,deny
    Deny from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# ===== ضغط الملفات =====
<IfModule mod_deflate.c>
    # ضغط النصوص والملفات
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/ld+json
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# ===== تخزين مؤقت للملفات =====
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # JSON
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/ld+json "access plus 1 hour"
</IfModule>

# ===== إعدادات Cache-Control =====
<IfModule mod_headers.c>
    # الصور والخطوط
    <FilesMatch "\.(ico|jpg|jpeg|png|gif|svg|webp|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # CSS و JavaScript
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # HTML
    <FilesMatch "\.(html|php)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
</IfModule>

# ===== تحسين الأداء =====

# تفعيل Keep-Alive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# ضغط إضافي للملفات الكبيرة
<IfModule mod_deflate.c>
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# ===== إعادة كتابة URLs =====

# إزالة .php من الروابط
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# إعادة توجيه للصفحة الرئيسية للروابط غير الموجودة
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# ===== دعم اللغات المتعددة =====

# إعداد اللغة الافتراضية
SetEnv DEFAULT_LANGUAGE ar

# إعادة توجيه حسب اللغة
RewriteCond %{HTTP:Accept-Language} ^en [NC]
RewriteRule ^$ /index.php?lang=en [L,R=302]

# ===== تحسين محركات البحث =====

# إعادة توجيه الروابط المكررة
RewriteCond %{THE_REQUEST} /+[^?]+?/+(\?.*)?(\s|\?|$) [NC]
RewriteRule ^(.*)$ /$1 [R=301,L]

# إزالة الشرطة المائلة الزائدة
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)/$ /$1 [R=301,L]

# ===== معالجة الأخطاء =====

# صفحات الأخطاء المخصصة
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# ===== حماية إضافية =====

# منع الهجمات الشائعة
RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|[|%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|[|%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>|ê|"|;|\?|\*|=$).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*("|'|<|>|\|{||).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(%0|%A|%B|%C|%D|%E|%F|127\.0).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
RewriteRule ^(.*)$ index.php [F,L]

# حماية من User Agents المشبوهة
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} ^.*(libwww-perl|curl|wget|python|nikto|scan) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} ^.*(<|>|'|%0A|%0D|%27|%3C|%3E|%00) [NC]
RewriteRule ^(.*)$ - [F,L]

# ===== إعدادات PHP (إذا كانت مدعومة) =====
<IfModule mod_php.c>
    # تحسين الذاكرة
    php_value memory_limit 256M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value post_max_size 32M
    php_value upload_max_filesize 32M
    
    # إعدادات الأمان
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log /path/to/error.log
    
    # تحسين الجلسات
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_only_cookies 1
</IfModule>
