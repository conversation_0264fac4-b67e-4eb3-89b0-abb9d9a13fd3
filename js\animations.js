// ===== Advanced Animations Controller =====

class AnimationController {
    constructor() {
        this.observers = new Map();
        this.animatedElements = new Set();
        this.init();
    }
    
    init() {
        this.setupScrollAnimations();
        this.setupParallaxEffects();
        this.setupCounterAnimations();
        this.setupTypewriterEffects();
        this.setupHoverAnimations();
        this.setupBackToTopButton();
        this.setupLoadingAnimations();
    }
    
    // ===== Scroll Animations =====
    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const scrollObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.animatedElements.has(entry.target)) {
                    this.triggerScrollAnimation(entry.target);
                    this.animatedElements.add(entry.target);
                }
            });
        }, observerOptions);
        
        // Observe all elements with scroll animations
        const scrollElements = document.querySelectorAll('.animate-on-scroll, .animate-fade-up, .animate-fade-left, .animate-fade-right, .animate-scale-in');
        scrollElements.forEach(element => {
            scrollObserver.observe(element);
        });
        
        this.observers.set('scroll', scrollObserver);
    }
    
    triggerScrollAnimation(element) {
        const delay = parseInt(element.getAttribute('data-delay')) || 0;
        
        setTimeout(() => {
            element.classList.add('animated');
            
            // Add specific animation classes based on element type
            if (element.classList.contains('animate-fade-up')) {
                element.style.animation = 'fadeUp 0.8s ease-out forwards';
            } else if (element.classList.contains('animate-fade-left')) {
                element.style.animation = 'fadeLeft 0.8s ease-out forwards';
            } else if (element.classList.contains('animate-fade-right')) {
                element.style.animation = 'fadeRight 0.8s ease-out forwards';
            } else if (element.classList.contains('animate-scale-in')) {
                element.style.animation = 'scaleIn 0.6s ease-out forwards';
            }
            
            // Trigger custom event
            element.dispatchEvent(new CustomEvent('animationTriggered', {
                detail: { element, delay }
            }));
        }, delay);
    }
    
    // ===== Parallax Effects =====
    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.parallax, .hero-particles');
        
        if (parallaxElements.length === 0) return;
        
        const handleParallax = this.throttle(() => {
            const scrolled = window.pageYOffset;
            
            parallaxElements.forEach(element => {
                const rate = scrolled * (element.dataset.speed || -0.5);
                const yPos = Math.round(rate);
                
                element.style.transform = `translateY(${yPos}px)`;
            });
        }, 16); // ~60fps
        
        window.addEventListener('scroll', handleParallax);
    }
    
    // ===== Counter Animations =====
    setupCounterAnimations() {
        const counters = document.querySelectorAll('.counter');
        
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                    this.animateCounter(entry.target);
                    entry.target.classList.add('counted');
                }
            });
        }, { threshold: 0.5 });
        
        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
        
        this.observers.set('counter', counterObserver);
    }
    
    animateCounter(element) {
        const target = parseInt(element.getAttribute('data-target'));
        const duration = parseInt(element.getAttribute('data-duration')) || 2000;
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                element.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
                element.classList.add('counter-complete');
            }
        };
        
        updateCounter();
    }
    
    // ===== Typewriter Effects =====
    setupTypewriterEffects() {
        const typewriterElements = document.querySelectorAll('.typewriter');
        
        typewriterElements.forEach(element => {
            this.createTypewriterEffect(element);
        });
    }
    
    createTypewriterEffect(element) {
        const text = element.textContent;
        const speed = parseInt(element.getAttribute('data-speed')) || 100;
        
        element.textContent = '';
        element.style.borderRight = '2px solid var(--primary-color)';
        
        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, speed);
            } else {
                // Blinking cursor effect
                setInterval(() => {
                    element.style.borderRight = element.style.borderRight === 'none' 
                        ? '2px solid var(--primary-color)' 
                        : 'none';
                }, 500);
            }
        };
        
        typeWriter();
    }
    
    // ===== Hover Animations =====
    setupHoverAnimations() {
        // Magnetic effect for buttons
        const magneticElements = document.querySelectorAll('.btn, .service-card, .pricing-card');
        
        magneticElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.addMagneticEffect(e.target);
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.removeMagneticEffect(e.target);
            });
        });
        
        // Tilt effect for cards
        const tiltElements = document.querySelectorAll('.feature-card, .service-card, .pricing-card');
        
        tiltElements.forEach(element => {
            element.addEventListener('mousemove', (e) => {
                this.addTiltEffect(e, element);
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.removeTiltEffect(element);
            });
        });
    }
    
    addMagneticEffect(element) {
        element.style.transition = 'transform 0.3s ease';
        
        element.addEventListener('mousemove', this.handleMagneticMove);
    }
    
    removeMagneticEffect(element) {
        element.style.transform = 'translate(0, 0)';
        element.removeEventListener('mousemove', this.handleMagneticMove);
    }
    
    handleMagneticMove = (e) => {
        const rect = e.target.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        const moveX = x * 0.1;
        const moveY = y * 0.1;
        
        e.target.style.transform = `translate(${moveX}px, ${moveY}px)`;
    }
    
    addTiltEffect(e, element) {
        const rect = element.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;
        
        element.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.02)`;
    }
    
    removeTiltEffect(element) {
        element.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)';
    }
    
    // ===== Back to Top Button =====
    setupBackToTopButton() {
        const backToTopBtn = document.getElementById('backToTop');
        
        if (!backToTopBtn) return;
        
        const toggleBackToTop = () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        };
        
        window.addEventListener('scroll', this.throttle(toggleBackToTop, 100));
        
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    // ===== Loading Animations =====
    setupLoadingAnimations() {
        // Page load animation
        window.addEventListener('load', () => {
            document.body.classList.add('loaded');
            
            // Trigger hero animations
            const heroElements = document.querySelectorAll('.hero .animate-fade-up, .hero .animate-fade-left');
            heroElements.forEach((element, index) => {
                setTimeout(() => {
                    element.classList.add('animated');
                }, index * 200);
            });
        });
        
        // Stagger animations for grids
        const grids = document.querySelectorAll('.features-grid, .services-grid, .pricing-grid');
        
        grids.forEach(grid => {
            const gridObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.staggerGridAnimation(entry.target);
                        gridObserver.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });
            
            gridObserver.observe(grid);
        });
    }
    
    staggerGridAnimation(grid) {
        const items = grid.querySelectorAll('.feature-card, .service-card, .pricing-card');
        
        items.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('animate-fade-up', 'animated');
            }, index * 100);
        });
    }
    
    // ===== Utility Functions =====
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // ===== Public Methods =====
    addCustomAnimation(element, animationName, options = {}) {
        const {
            duration = '0.8s',
            easing = 'ease-out',
            delay = '0s',
            fillMode = 'forwards'
        } = options;
        
        element.style.animation = `${animationName} ${duration} ${easing} ${delay} ${fillMode}`;
    }
    
    removeAnimation(element) {
        element.style.animation = '';
        element.classList.remove('animated');
    }
    
    // ===== Cleanup =====
    destroy() {
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers.clear();
        this.animatedElements.clear();
    }
}

// Initialize animation controller when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.animationController = new AnimationController();
    
    // Add smooth scrolling for all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnimationController;
}
